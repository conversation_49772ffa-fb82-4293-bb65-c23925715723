#!/usr/bin/env python3
"""
LP Payoff Visualization for Prediction Market
Using invariant: X^P * Y^(1-P) = k
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.optimize import fsolve
import warnings
warnings.filterwarnings('ignore')

class PredictionMarketLP:
    def __init__(self, k=1.0, initial_x=1.0, initial_y=1.0):
        """
        Initialize prediction market LP analyzer

        Args:
            k: Invariant constant
            initial_x: Initial YES token reserves
            initial_y: Initial NO token reserves
        """
        self.k = k
        self.initial_x = initial_x
        self.initial_y = initial_y

    def calculate_reserves_at_probability(self, p):
        """
        Calculate equilibrium reserves at given probability

        Args:
            p: Probability of YES outcome (0 to 1)

        Returns:
            tuple: (x_reserves, y_reserves)
        """
        if p == 0:
            return (0, self.k)
        elif p == 1:
            return (self.k, 0)
        elif p == 0.5:
            # Special case: constant product AMM
            return (np.sqrt(self.k), np.sqrt(self.k))
        else:
            # Solve for equilibrium reserves
            def equations(vars):
                x, y = vars
                eq1 = x**p * y**(1-p) - self.k
                # Additional constraint: marginal rate condition
                # This ensures we're at the right point on the curve
                eq2 = (p * y) / ((1-p) * x) - 1  # Simplified equilibrium condition
                return [eq1, eq2]

            try:
                # Initial guess based on probability weighting
                x_guess = self.k ** (1/(2-p)) if p < 1 else self.k
                y_guess = self.k ** (1/(1+p)) if p > 0 else self.k

                x, y = fsolve(equations, [x_guess, y_guess])

                # Ensure positive values
                x = max(x, 1e-10)
                y = max(y, 1e-10)

                return (x, y)
            except:
                # Fallback calculation
                if p < 0.5:
                    x = self.k ** (1/(1+p))
                    y = (self.k / (x**p)) ** (1/(1-p))
                else:
                    y = self.k ** (1/(2-p))
                    x = (self.k / (y**(1-p))) ** (1/p)

                return (max(x, 1e-10), max(y, 1e-10))

    def lp_payoff(self, p):
        """
        Calculate LP expected payoff at probability p

        Args:
            p: Probability of YES outcome

        Returns:
            float: Expected LP payoff
        """
        x, y = self.calculate_reserves_at_probability(p)
        return p * x + (1 - p) * y

    def plot_lp_payoff(self, p_range=None, title="LP Payoff vs Probability"):
        """
        Plot LP payoff function

        Args:
            p_range: Range of probabilities to plot
            title: Plot title
        """
        if p_range is None:
            p_range = np.linspace(0.01, 0.99, 100)

        payoffs = [self.lp_payoff(p) for p in p_range]

        plt.figure(figsize=(12, 8))
        plt.plot(p_range, payoffs, 'b-', linewidth=2, label='LP Expected Payoff')

        # Add special points
        payoff_05 = self.lp_payoff(0.5)
        plt.plot(0.5, payoff_05, 'ro', markersize=8, label=f'P=0.5: {payoff_05:.3f}')

        # Add horizontal line for initial value
        initial_value = 0.5 * self.initial_x + 0.5 * self.initial_y
        plt.axhline(y=initial_value, color='gray', linestyle='--', alpha=0.7,
                   label=f'Initial Value: {initial_value:.3f}')

        plt.xlabel('Probability of YES Outcome', fontsize=12)
        plt.ylabel('LP Expected Payoff', fontsize=12)
        plt.title(title, fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.xlim(0, 1)

        # Add annotations
        plt.annotate('Higher payoff at\nextreme probabilities',
                    xy=(0.1, self.lp_payoff(0.1)), xytext=(0.2, self.lp_payoff(0.1) + 0.1),
                    arrowprops=dict(arrowstyle='->', color='red', alpha=0.7),
                    fontsize=10, ha='center')

        plt.tight_layout()
        return plt.gcf()

    def plot_reserves_vs_probability(self):
        """
        Plot how reserves change with probability
        """
        p_range = np.linspace(0.01, 0.99, 100)
        x_reserves = []
        y_reserves = []

        for p in p_range:
            x, y = self.calculate_reserves_at_probability(p)
            x_reserves.append(x)
            y_reserves.append(y)

        plt.figure(figsize=(12, 6))
        plt.plot(p_range, x_reserves, 'g-', linewidth=2, label='YES Token Reserves (X)')
        plt.plot(p_range, y_reserves, 'r-', linewidth=2, label='NO Token Reserves (Y)')

        plt.xlabel('Probability of YES Outcome', fontsize=12)
        plt.ylabel('Token Reserves', fontsize=12)
        plt.title('Token Reserves vs Probability', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.xlim(0, 1)
        plt.tight_layout()
        return plt.gcf()

    def analyze_impermanent_loss(self):
        """
        Analyze impermanent loss across different probabilities
        """
        p_range = np.linspace(0.01, 0.99, 100)
        payoffs = [self.lp_payoff(p) for p in p_range]

        # Calculate impermanent loss relative to holding equal amounts
        hold_value = 0.5 * self.initial_x + 0.5 * self.initial_y
        impermanent_loss = [(hold_value - payoff) / hold_value * 100 for payoff in payoffs]

        plt.figure(figsize=(12, 6))
        plt.plot(p_range, impermanent_loss, 'purple', linewidth=2)
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)

        plt.xlabel('Probability of YES Outcome', fontsize=12)
        plt.ylabel('Impermanent Loss (%)', fontsize=12)
        plt.title('Impermanent Loss vs Probability', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.xlim(0, 1)

        # Find minimum impermanent loss
        min_loss_idx = np.argmin(np.abs(impermanent_loss))
        min_loss_p = p_range[min_loss_idx]
        min_loss_value = impermanent_loss[min_loss_idx]

        plt.plot(min_loss_p, min_loss_value, 'ro', markersize=8,
                label=f'Min Loss at P={min_loss_p:.3f}: {min_loss_value:.2f}%')
        plt.legend()
        plt.tight_layout()
        return plt.gcf()

def main():
    """
    Main function to generate all visualizations
    """
    print("Generating LP Payoff Visualizations for Prediction Market")
    print("=" * 60)

    # Initialize market with k=1, equal initial reserves
    market = PredictionMarketLP(k=1.0, initial_x=1.0, initial_y=1.0)

    # Generate probability range
    probabilities = np.linspace(0.01, 0.99, 100)

    # Calculate and display key metrics
    print("\nKey Metrics:")
    print(f"Initial X (YES) reserves: {market.initial_x}")
    print(f"Initial Y (NO) reserves: {market.initial_y}")
    print(f"Invariant constant k: {market.k}")

    # Calculate payoffs at key probabilities
    key_probs = [0.1, 0.25, 0.5, 0.75, 0.9]
    print(f"\nLP Payoffs at key probabilities:")
    for p in key_probs:
        payoff = market.lp_payoff(p)
        x, y = market.calculate_reserves_at_probability(p)
        print(f"P = {p:4.2f}: Payoff = {payoff:6.4f}, X = {x:6.4f}, Y = {y:6.4f}")

    # Closed-form solution explanation
    print(f"\n" + "="*60)
    print("CLOSED-FORM LP PAYOFF SOLUTION:")
    print("="*60)
    print("Given invariant: X^P * Y^(1-P) = k")
    print("LP Expected Payoff = P * X(P) + (1-P) * Y(P)")
    print("")
    print("Where X(P) and Y(P) are equilibrium reserves at probability P")
    print("satisfying the invariant constraint.")
    print("")
    print("For the general case with k=1:")
    print("- At P=0.5: Payoff = 0.5*X + 0.5*Y = √k = 1")
    print("- At P→0:   Payoff ≈ Y = k = 1")
    print("- At P→1:   Payoff ≈ X = k = 1")
    print("")
    print("The LP payoff is minimized around P=0.5 and maximized at extreme probabilities.")

    # Create visualizations
    print(f"\nGenerating visualizations...")

    # 1. Main LP payoff plot
    fig1 = market.plot_lp_payoff()
    plt.savefig('lp_payoff_vs_probability.png', dpi=300, bbox_inches='tight')
    print("✓ Saved: lp_payoff_vs_probability.png")

    # 2. Reserves vs probability
    fig2 = market.plot_reserves_vs_probability()
    plt.savefig('reserves_vs_probability.png', dpi=300, bbox_inches='tight')
    print("✓ Saved: reserves_vs_probability.png")

    # 3. Impermanent loss analysis
    fig3 = market.analyze_impermanent_loss()
    plt.savefig('impermanent_loss_analysis.png', dpi=300, bbox_inches='tight')
    print("✓ Saved: impermanent_loss_analysis.png")

    # 4. Combined analysis plot
    fig4, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # Subplot 1: LP Payoff
    payoffs = [market.lp_payoff(p) for p in probabilities]
    ax1.plot(probabilities, payoffs, 'b-', linewidth=2)
    ax1.set_xlabel('Probability of YES')
    ax1.set_ylabel('LP Expected Payoff')
    ax1.set_title('LP Payoff Function')
    ax1.grid(True, alpha=0.3)
    ax1.axhline(y=1.0, color='gray', linestyle='--', alpha=0.7)

    # Subplot 2: Reserves
    x_reserves = []
    y_reserves = []
    for p in probabilities:
        x, y = market.calculate_reserves_at_probability(p)
        x_reserves.append(x)
        y_reserves.append(y)

    ax2.plot(probabilities, x_reserves, 'g-', linewidth=2, label='YES Reserves')
    ax2.plot(probabilities, y_reserves, 'r-', linewidth=2, label='NO Reserves')
    ax2.set_xlabel('Probability of YES')
    ax2.set_ylabel('Token Reserves')
    ax2.set_title('Token Reserves vs Probability')
    ax2.grid(True, alpha=0.3)
    ax2.legend()

    # Subplot 3: Invariant verification
    invariant_values = [x**p * y**(1-p) for p, x, y in zip(probabilities, x_reserves, y_reserves)]
    ax3.plot(probabilities, invariant_values, 'purple', linewidth=2)
    ax3.axhline(y=market.k, color='black', linestyle='--', alpha=0.7, label=f'k = {market.k}')
    ax3.set_xlabel('Probability of YES')
    ax3.set_ylabel('X^P * Y^(1-P)')
    ax3.set_title('Invariant Verification')
    ax3.grid(True, alpha=0.3)
    ax3.legend()

    # Subplot 4: Price impact
    prices = []
    for i in range(len(probabilities)-1):
        dp = probabilities[i+1] - probabilities[i]
        dx = x_reserves[i+1] - x_reserves[i]
        if dx != 0:
            price_impact = abs(dp / dx)
            prices.append(price_impact)
        else:
            prices.append(0)

    ax4.plot(probabilities[:-1], prices, 'orange', linewidth=2)
    ax4.set_xlabel('Probability of YES')
    ax4.set_ylabel('Price Impact (dP/dX)')
    ax4.set_title('Price Impact Analysis')
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('combined_analysis.png', dpi=300, bbox_inches='tight')
    print("✓ Saved: combined_analysis.png")

    print(f"\nAll visualizations generated successfully!")
    print("Files created:")
    print("- lp_payoff_vs_probability.png")
    print("- reserves_vs_probability.png")
    print("- impermanent_loss_analysis.png")
    print("- combined_analysis.png")

    # Show plots
    plt.show()

if __name__ == "__main__":
    main()