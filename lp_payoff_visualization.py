#!/usr/bin/env python3
"""
LP Payoff Visualization for Prediction Market
Based on LP payoff function: V = x^(1-P_yes) * (1-x)^(P_yes)
Using derived invariant: X^(P_yes) * Y^(1-P_yes) = K
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

class PredictionMarketLP:
    def __init__(self, total_reserves=1.0):
        """
        Initialize prediction market LP analyzer

        Args:
            total_reserves: Total reserves (X + Y)
        """
        self.total_reserves = total_reserves

    def lp_payoff_function(self, x, p_yes):
        """
        Calculate LP payoff using the given formula: V = x^(1-P_yes) * (1-x)^(P_yes)

        Args:
            x: Fraction of YES tokens in portfolio (0 to 1)
            p_yes: Probability of YES outcome (0 to 1)

        Returns:
            float: LP payoff value
        """
        if x <= 0 or x >= 1:
            return 0
        return (x**(1-p_yes)) * ((1-x)**p_yes)

    def optimal_allocation(self, p_yes):
        """
        Calculate optimal allocation that maximizes LP payoff

        Args:
            p_yes: Probability of YES outcome

        Returns:
            float: Optimal fraction of YES tokens (x*)
        """
        # From derivative: x* = 1 - p_yes
        return 1 - p_yes

    def max_lp_payoff(self, p_yes):
        """
        Calculate maximum LP payoff at given probability

        Args:
            p_yes: Probability of YES outcome

        Returns:
            float: Maximum LP payoff
        """
        x_optimal = self.optimal_allocation(p_yes)
        return self.lp_payoff_function(x_optimal, p_yes)

    def calculate_reserves_from_allocation(self, x, total_reserves=None):
        """
        Calculate YES and NO reserves from allocation fraction

        Args:
            x: Fraction of YES tokens
            total_reserves: Total reserves (default: self.total_reserves)

        Returns:
            tuple: (yes_reserves, no_reserves)
        """
        if total_reserves is None:
            total_reserves = self.total_reserves

        yes_reserves = x * total_reserves
        no_reserves = (1 - x) * total_reserves
        return yes_reserves, no_reserves

    def plot_lp_payoff(self, p_range=None, title="LP Payoff vs Probability"):
        """
        Plot LP payoff function

        Args:
            p_range: Range of probabilities to plot
            title: Plot title
        """
        if p_range is None:
            p_range = np.linspace(0.01, 0.99, 100)

        payoffs = [self.lp_payoff(p) for p in p_range]

        plt.figure(figsize=(12, 8))
        plt.plot(p_range, payoffs, 'b-', linewidth=2, label='LP Expected Payoff')

        # Add special points
        payoff_05 = self.lp_payoff(0.5)
        plt.plot(0.5, payoff_05, 'ro', markersize=8, label=f'P=0.5: {payoff_05:.3f}')

        # Add horizontal line for initial value
        initial_value = 0.5 * self.initial_x + 0.5 * self.initial_y
        plt.axhline(y=initial_value, color='gray', linestyle='--', alpha=0.7,
                   label=f'Initial Value: {initial_value:.3f}')

        plt.xlabel('Probability of YES Outcome', fontsize=12)
        plt.ylabel('LP Expected Payoff', fontsize=12)
        plt.title(title, fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.xlim(0, 1)

        # Add annotations
        plt.annotate('Higher payoff at\nextreme probabilities',
                    xy=(0.1, self.lp_payoff(0.1)), xytext=(0.2, self.lp_payoff(0.1) + 0.1),
                    arrowprops=dict(arrowstyle='->', color='red', alpha=0.7),
                    fontsize=10, ha='center')

        plt.tight_layout()
        return plt.gcf()

    def plot_reserves_vs_probability(self):
        """
        Plot how reserves change with probability
        """
        p_range = np.linspace(0.01, 0.99, 100)
        x_reserves = []
        y_reserves = []

        for p in p_range:
            x, y = self.calculate_reserves_at_probability(p)
            x_reserves.append(x)
            y_reserves.append(y)

        plt.figure(figsize=(12, 6))
        plt.plot(p_range, x_reserves, 'g-', linewidth=2, label='YES Token Reserves (X)')
        plt.plot(p_range, y_reserves, 'r-', linewidth=2, label='NO Token Reserves (Y)')

        plt.xlabel('Probability of YES Outcome', fontsize=12)
        plt.ylabel('Token Reserves', fontsize=12)
        plt.title('Token Reserves vs Probability', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.xlim(0, 1)
        plt.tight_layout()
        return plt.gcf()

    def analyze_impermanent_loss(self):
        """
        Analyze impermanent loss across different probabilities
        """
        p_range = np.linspace(0.01, 0.99, 100)
        payoffs = [self.lp_payoff(p) for p in p_range]

        # Calculate impermanent loss relative to holding equal amounts
        hold_value = 0.5 * self.initial_x + 0.5 * self.initial_y
        impermanent_loss = [(hold_value - payoff) / hold_value * 100 for payoff in payoffs]

        plt.figure(figsize=(12, 6))
        plt.plot(p_range, impermanent_loss, 'purple', linewidth=2)
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)

        plt.xlabel('Probability of YES Outcome', fontsize=12)
        plt.ylabel('Impermanent Loss (%)', fontsize=12)
        plt.title('Impermanent Loss vs Probability', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.xlim(0, 1)

        # Find minimum impermanent loss
        min_loss_idx = np.argmin(np.abs(impermanent_loss))
        min_loss_p = p_range[min_loss_idx]
        min_loss_value = impermanent_loss[min_loss_idx]

        plt.plot(min_loss_p, min_loss_value, 'ro', markersize=8,
                label=f'Min Loss at P={min_loss_p:.3f}: {min_loss_value:.2f}%')
        plt.legend()
        plt.tight_layout()
        return plt.gcf()

def plot_lp_payoff_vs_probability():
    """
    Create a plot of LP payoff vs probability using the derived formula
    V = x^(1-P_yes) * (1-x)^(P_yes) where x = 1 - P_yes (optimal allocation)
    """
    # Initialize market
    market = PredictionMarketLP(total_reserves=1.0)

    # Generate probability range
    probabilities = np.linspace(0.01, 0.99, 200)

    # Calculate maximum LP payoffs (at optimal allocation)
    max_payoffs = [market.max_lp_payoff(p) for p in probabilities]

    # Create the plot
    plt.figure(figsize=(12, 8))
    plt.plot(probabilities, max_payoffs, 'b-', linewidth=3, label='Maximum LP Payoff')

    # Add key points
    key_points = [0.1, 0.25, 0.5, 0.75, 0.9]
    for i, p in enumerate(key_points):
        payoff = market.max_lp_payoff(p)
        x_optimal = market.optimal_allocation(p)
        plt.plot(p, payoff, 'ro', markersize=8)

        # Alternate annotation positions to avoid overlap
        if i % 2 == 0:
            xytext = (15, 15)
        else:
            xytext = (15, -25)

        plt.annotate(f'P_yes={p:.1f}\nV_max={payoff:.3f}\nx*={x_optimal:.1f}',
                    xy=(p, payoff), xytext=xytext,
                    textcoords='offset points', fontsize=10,
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8),
                    arrowprops=dict(arrowstyle='->', color='red', alpha=0.7))

    # Formatting
    plt.xlabel('Probability of YES Outcome (P_yes)', fontsize=16)
    plt.ylabel('Maximum LP Payoff (V)', fontsize=16)
    plt.title('LP Payoff vs Probability of YES\nV = x^(1-P_yes) * (1-x)^(P_yes) at optimal x* = 1-P_yes',
              fontsize=16, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=14)
    plt.xlim(0, 1)
    plt.ylim(0, max(max_payoffs) * 1.1)

    # Add mathematical formula as text
    formula_text = ('Mathematical Formula:\n'
                   'V = x^(1-P_yes) * (1-x)^(P_yes)\n'
                   'Optimal allocation: x* = 1 - P_yes\n'
                   'Invariant: X^(P_yes) * Y^(1-P_yes) = K')

    plt.text(0.02, 0.98, formula_text,
             transform=plt.gca().transAxes, fontsize=11,
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8),
             verticalalignment='top')

    plt.tight_layout()
    plt.savefig('lp_payoff_legendre_transform.png', dpi=300, bbox_inches='tight')
    print("✓ Saved: lp_payoff_legendre_transform.png")
    plt.show()

def plot_lp_payoff_surface():
    """
    Create a 3D surface plot showing LP payoff as function of x and P_yes
    """
    # Create meshgrid
    x_range = np.linspace(0.01, 0.99, 50)
    p_range = np.linspace(0.01, 0.99, 50)
    X, P = np.meshgrid(x_range, p_range)

    # Calculate LP payoff surface
    market = PredictionMarketLP()
    V = np.zeros_like(X)
    for i in range(len(p_range)):
        for j in range(len(x_range)):
            V[i, j] = market.lp_payoff_function(X[i, j], P[i, j])

    # Create 3D plot
    fig = plt.figure(figsize=(14, 10))
    ax = fig.add_subplot(111, projection='3d')

    # Surface plot
    surf = ax.plot_surface(X, P, V, cmap='viridis', alpha=0.8)

    # Add optimal path (x* = 1 - P_yes)
    p_optimal = np.linspace(0.01, 0.99, 100)
    x_optimal = 1 - p_optimal
    v_optimal = [market.lp_payoff_function(x, p) for x, p in zip(x_optimal, p_optimal)]
    ax.plot(x_optimal, p_optimal, v_optimal, 'r-', linewidth=4, label='Optimal Path: x* = 1-P_yes')

    # Formatting
    ax.set_xlabel('Allocation Fraction (x)', fontsize=12)
    ax.set_ylabel('Probability of YES (P_yes)', fontsize=12)
    ax.set_zlabel('LP Payoff (V)', fontsize=12)
    ax.set_title('LP Payoff Surface: V = x^(1-P_yes) * (1-x)^(P_yes)', fontsize=14, fontweight='bold')

    # Add colorbar
    fig.colorbar(surf, shrink=0.5, aspect=5)
    ax.legend()

    plt.tight_layout()
    plt.savefig('lp_payoff_surface.png', dpi=300, bbox_inches='tight')
    print("✓ Saved: lp_payoff_surface.png")
    plt.show()

def main():
    """
    Main function to generate LP payoff visualizations based on Legendre transform
    """
    print("Generating LP Payoff Visualizations")
    print("Based on: V = x^(1-P_yes) * (1-x)^(P_yes)")
    print("Derived Invariant: X^(P_yes) * Y^(1-P_yes) = K")
    print("=" * 60)

    # Initialize market
    market = PredictionMarketLP(total_reserves=1.0)

    # Display key mathematical insights
    print("\nKey Mathematical Insights:")
    print("- LP Payoff Function: V = x^(1-P_yes) * (1-x)^(P_yes)")
    print("- Optimal Allocation: x* = 1 - P_yes")
    print("- Maximum Payoff: V_max = (1-P_yes)^(1-P_yes) * (P_yes)^(P_yes)")
    print("- Derived Invariant: X^(P_yes) * Y^(1-P_yes) = K")

    # Calculate and display payoffs at key probabilities
    key_probs = [0.1, 0.25, 0.5, 0.75, 0.9]
    print(f"\nLP Payoffs at key probabilities:")
    for p in key_probs:
        x_opt = market.optimal_allocation(p)
        max_payoff = market.max_lp_payoff(p)
        print(f"P_yes = {p:4.2f}: x* = {x_opt:5.2f}, V_max = {max_payoff:6.4f}")

    print(f"\nGenerating visualizations...")

    # Create 2D plot
    plot_lp_payoff_vs_probability()

    # Create 3D surface plot
    plot_lp_payoff_surface()

    print(f"\nAll visualizations generated successfully!")
    print("Files created:")
    print("- lp_payoff_legendre_transform.png")
    print("- lp_payoff_surface.png")

if __name__ == "__main__":
    main()