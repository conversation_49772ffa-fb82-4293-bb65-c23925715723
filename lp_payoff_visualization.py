#!/usr/bin/env python3
"""
LP Payoff Visualization for Prediction Market
Using invariant: X^P * Y^(1-P) = k
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.optimize import fsolve
import warnings
warnings.filterwarnings('ignore')

class PredictionMarketLP:
    def __init__(self, k=1.0, initial_x=1.0, initial_y=1.0):
        """
        Initialize prediction market LP analyzer

        Args:
            k: Invariant constant
            initial_x: Initial YES token reserves
            initial_y: Initial NO token reserves
        """
        self.k = k
        self.initial_x = initial_x
        self.initial_y = initial_y

    def calculate_reserves_at_probability(self, p):
        """
        Calculate equilibrium reserves at given probability

        Args:
            p: Probability of YES outcome (0 to 1)

        Returns:
            tuple: (x_reserves, y_reserves)
        """
        if p == 0:
            return (0, self.k)
        elif p == 1:
            return (self.k, 0)
        elif p == 0.5:
            # Special case: constant product AMM
            return (np.sqrt(self.k), np.sqrt(self.k))
        else:
            # Solve for equilibrium reserves
            def equations(vars):
                x, y = vars
                eq1 = x**p * y**(1-p) - self.k
                # Additional constraint: marginal rate condition
                # This ensures we're at the right point on the curve
                eq2 = (p * y) / ((1-p) * x) - 1  # Simplified equilibrium condition
                return [eq1, eq2]

            try:
                # Initial guess based on probability weighting
                x_guess = self.k ** (1/(2-p)) if p < 1 else self.k
                y_guess = self.k ** (1/(1+p)) if p > 0 else self.k

                x, y = fsolve(equations, [x_guess, y_guess])

                # Ensure positive values
                x = max(x, 1e-10)
                y = max(y, 1e-10)

                return (x, y)
            except:
                # Fallback calculation
                if p < 0.5:
                    x = self.k ** (1/(1+p))
                    y = (self.k / (x**p)) ** (1/(1-p))
                else:
                    y = self.k ** (1/(2-p))
                    x = (self.k / (y**(1-p))) ** (1/p)

                return (max(x, 1e-10), max(y, 1e-10))

    def lp_payoff(self, p):
        """
        Calculate LP expected payoff at probability p

        Args:
            p: Probability of YES outcome

        Returns:
            float: Expected LP payoff
        """
        x, y = self.calculate_reserves_at_probability(p)
        return p * x + (1 - p) * y

    def plot_lp_payoff(self, p_range=None, title="LP Payoff vs Probability"):
        """
        Plot LP payoff function

        Args:
            p_range: Range of probabilities to plot
            title: Plot title
        """
        if p_range is None:
            p_range = np.linspace(0.01, 0.99, 100)

        payoffs = [self.lp_payoff(p) for p in p_range]

        plt.figure(figsize=(12, 8))
        plt.plot(p_range, payoffs, 'b-', linewidth=2, label='LP Expected Payoff')

        # Add special points
        payoff_05 = self.lp_payoff(0.5)
        plt.plot(0.5, payoff_05, 'ro', markersize=8, label=f'P=0.5: {payoff_05:.3f}')

        # Add horizontal line for initial value
        initial_value = 0.5 * self.initial_x + 0.5 * self.initial_y
        plt.axhline(y=initial_value, color='gray', linestyle='--', alpha=0.7,
                   label=f'Initial Value: {initial_value:.3f}')

        plt.xlabel('Probability of YES Outcome', fontsize=12)
        plt.ylabel('LP Expected Payoff', fontsize=12)
        plt.title(title, fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.xlim(0, 1)

        # Add annotations
        plt.annotate('Higher payoff at\nextreme probabilities',
                    xy=(0.1, self.lp_payoff(0.1)), xytext=(0.2, self.lp_payoff(0.1) + 0.1),
                    arrowprops=dict(arrowstyle='->', color='red', alpha=0.7),
                    fontsize=10, ha='center')

        plt.tight_layout()
        return plt.gcf()

    def plot_reserves_vs_probability(self):
        """
        Plot how reserves change with probability
        """
        p_range = np.linspace(0.01, 0.99, 100)
        x_reserves = []
        y_reserves = []

        for p in p_range:
            x, y = self.calculate_reserves_at_probability(p)
            x_reserves.append(x)
            y_reserves.append(y)

        plt.figure(figsize=(12, 6))
        plt.plot(p_range, x_reserves, 'g-', linewidth=2, label='YES Token Reserves (X)')
        plt.plot(p_range, y_reserves, 'r-', linewidth=2, label='NO Token Reserves (Y)')

        plt.xlabel('Probability of YES Outcome', fontsize=12)
        plt.ylabel('Token Reserves', fontsize=12)
        plt.title('Token Reserves vs Probability', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.xlim(0, 1)
        plt.tight_layout()
        return plt.gcf()

    def analyze_impermanent_loss(self):
        """
        Analyze impermanent loss across different probabilities
        """
        p_range = np.linspace(0.01, 0.99, 100)
        payoffs = [self.lp_payoff(p) for p in p_range]

        # Calculate impermanent loss relative to holding equal amounts
        hold_value = 0.5 * self.initial_x + 0.5 * self.initial_y
        impermanent_loss = [(hold_value - payoff) / hold_value * 100 for payoff in payoffs]

        plt.figure(figsize=(12, 6))
        plt.plot(p_range, impermanent_loss, 'purple', linewidth=2)
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)

        plt.xlabel('Probability of YES Outcome', fontsize=12)
        plt.ylabel('Impermanent Loss (%)', fontsize=12)
        plt.title('Impermanent Loss vs Probability', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.xlim(0, 1)

        # Find minimum impermanent loss
        min_loss_idx = np.argmin(np.abs(impermanent_loss))
        min_loss_p = p_range[min_loss_idx]
        min_loss_value = impermanent_loss[min_loss_idx]

        plt.plot(min_loss_p, min_loss_value, 'ro', markersize=8,
                label=f'Min Loss at P={min_loss_p:.3f}: {min_loss_value:.2f}%')
        plt.legend()
        plt.tight_layout()
        return plt.gcf()

def simple_lp_payoff_plot():
    """
    Create a simple, clean plot of LP payoff vs probability
    """
    # Initialize market with k=1, equal initial reserves
    market = PredictionMarketLP(k=1.0, initial_x=1.0, initial_y=1.0)

    # Generate probability range
    probabilities = np.linspace(0.01, 0.99, 200)

    # Calculate LP payoffs
    payoffs = [market.lp_payoff(p) for p in probabilities]

    # Create the plot
    plt.figure(figsize=(10, 6))
    plt.plot(probabilities, payoffs, 'b-', linewidth=3, label='LP Expected Payoff')

    # Add reference line at initial value
    plt.axhline(y=1.0, color='gray', linestyle='--', alpha=0.7, label='Initial Value (k=1)')

    # Formatting
    plt.xlabel('Probability of YES Outcome', fontsize=14)
    plt.ylabel('LP Expected Payoff', fontsize=14)
    plt.title('LP Payoff vs Probability of YES\nInvariant: X^P * Y^(1-P) = k', fontsize=16, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=12)
    plt.xlim(0, 1)

    # Add key points
    key_points = [0.1, 0.25, 0.5, 0.75, 0.9]
    for p in key_points:
        payoff = market.lp_payoff(p)
        plt.plot(p, payoff, 'ro', markersize=6)
        plt.annotate(f'P={p:.1f}\nPayoff={payoff:.3f}',
                    xy=(p, payoff), xytext=(10, 10),
                    textcoords='offset points', fontsize=9,
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))

    plt.tight_layout()
    plt.savefig('simple_lp_payoff.png', dpi=300, bbox_inches='tight')
    print("✓ Saved: simple_lp_payoff.png")
    plt.show()

def main():
    """
    Main function to generate simple LP payoff visualization
    """
    print("Generating Simple LP Payoff Plot")
    print("=" * 40)

    # Create simple plot
    simple_lp_payoff_plot()

    print("\nPlot generated successfully!")
    print("File created: simple_lp_payoff.png")

if __name__ == "__main__":
    main()