# Prediction Market with Legendre Transform-Derived Invariant

A comprehensive implementation of a prediction market derived from the LP payoff function **V = x^(1-P_yes) * (1-x)^(P_yes)** using <PERSON><PERSON> transform to obtain the invariant **X^(P_yes) * Y^(1-P_yes) = K**.

## 🎯 Mathematical Foundation

### Given LP Payoff Function
**V(x, P_yes) = x^(1-P_yes) * (1-x)^(P_yes)**

Where:
- V = LP Payoff value
- x = fraction of YES tokens in portfolio (0 ≤ x ≤ 1)
- P_yes = probability of YES outcome (0 ≤ P_yes ≤ 1)

This is a **concave function** where the peak occurs at **x* = 1 - P_yes**.

### Derived Invariant via Legendre Transform
Through Legendre transform analysis, the market invariant becomes:

**X^(P_yes) * Y^(1-P_yes) = K**

Where:
- X = YES token reserves
- Y = NO token reserves
- K = invariant constant

## 📊 Key Features

- **Probability-Weighted Invariant**: X^P * Y^(1-P) = k
- **Dynamic Probability Updates**: Market probability can be updated by the owner
- **Liquidity Provision**: Users can provide liquidity and earn fees
- **Trading Mechanism**: Swap between YES and NO tokens
- **Market Resolution**: Resolve markets and claim winnings
- **LP Payoff Analysis**: Mathematical analysis and visualization of LP returns

## 🧮 Mathematical Foundation

### Invariant Formula
```
X^P * Y^(1-P) = k
```

Where:
- **X** = YES token reserves
- **Y** = NO token reserves
- **P** = Probability of YES outcome (0 ≤ P ≤ 1)
- **k** = Invariant constant

### LP Payoff Formula (Closed-Form Solution)

**Expected LP Payoff = P * X(P) + (1-P) * Y(P)**

Where X(P) and Y(P) are the equilibrium reserves at probability P satisfying the invariant constraint.

#### Key Properties:
1. **At P = 0.5**: Reduces to constant product AMM behavior
2. **At P → 0**: LP payoff approaches Y reserves (NO tokens)
3. **At P → 1**: LP payoff approaches X reserves (YES tokens)
4. **Minimum payoff**: Typically occurs around P = 0.5
5. **Maximum payoff**: Occurs at extreme probabilities (P → 0 or P → 1)

## 📁 Project Structure

```
├── PredictionMarket.sol          # Main Solidity contract
├── PredictionMarketTest.sol      # Comprehensive test suite
├── lp_payoff_visualization.py    # Python visualization script
├── LP_Payoff_Analysis.md         # Mathematical analysis
├── README.md                     # This file
└── Generated Visualizations:
    ├── lp_payoff_vs_probability.png
    ├── reserves_vs_probability.png
    ├── impermanent_loss_analysis.png
    └── combined_analysis.png
```

## 🔧 Smart Contract Features

### Core Functions (Updated)

- `mintLP(address to, uint256 yesIn, uint256 noIn)` - Mint LP tokens with probability-weighted proportional deposits
- `burnLP(uint256 liquidity)` - Burn LP tokens and withdraw proportional reserves
- `swapYesForNo(uint256 yesIn, address to)` - Swap YES tokens for NO tokens using weighted invariant
- `swapNoForYes(uint256 noIn, address to)` - Swap NO tokens for YES tokens using weighted invariant
- `updateProbability(uint256 newProbability)` - Update market probability (owner only)
- `resolveMarket(bool yesWins)` - Resolve the market (owner only)

### View Functions (Updated)

- `getReserves()` - Get current YES and NO reserves
- `calculateK(uint256 x, uint256 y)` - Calculate invariant constant using X^P * Y^(1-P)
- `calculateNoFromYes(uint256 yesAmount, uint256 currentK)` - Calculate NO reserves from YES using invariant
- `calculateYesFromNo(uint256 noAmount, uint256 currentK)` - Calculate YES reserves from NO using invariant
- `calculateCurrentProbability()` - Calculate current market probability based on reserves

## 🔧 Key Changes Made

### 1. **Probability-Controlled Weights**
```solidity
// OLD: Fixed weights X * Y² = k
uint public constant YES_WEIGHT = 1;
uint public constant NO_WEIGHT = 2;

// NEW: Dynamic weights X^P * Y^(1-P) = k
uint256 public probability; // P parameter (0 to PRECISION)
```

### 2. **Updated Liquidity Functions**
```solidity
// Weighted proportional deposit check:
// YesIn * NoReserve^(1-P) == NoIn * YesReserve^P
uint256 leftSide = yesIn * pow(noReserve, PRECISION - probability, PRECISION);
uint256 rightSide = noIn * pow(yesReserve, probability, PRECISION);
require(leftSide == rightSide, "UNBALANCED_WEIGHTED");
```

### 3. **Probability-Weighted Swaps**
```solidity
// Calculate new reserves using: X^P * Y^(1-P) = K
uint256 newNo = calculateNoFromYes(newYes, currentK);
uint256 newYes = calculateYesFromNo(newNo, currentK);
```

## 📈 Visualizations

The project includes comprehensive visualizations showing:

1. **LP Payoff vs Probability**: How LP returns vary with market probability
2. **Token Reserves vs Probability**: How reserves change as probability shifts
3. **Impermanent Loss Analysis**: LP losses relative to holding tokens
4. **Combined Analysis**: Multi-panel view of all metrics

### Running Visualizations

```bash
python3 lp_payoff_visualization.py
```

## 🧪 Testing

Comprehensive test suite covering:

- Initial liquidity provision
- Proportional liquidity addition
- Token trading (both directions)
- Slippage protection
- Probability updates
- Market resolution
- Winnings claims
- Invariant maintenance

### Running Tests

```bash
forge test
```

## 🔍 Key Insights

### LP Payoff Behavior

1. **Symmetric Markets (P ≈ 0.5)**: LP payoff is minimized, similar to traditional AMMs
2. **Extreme Probabilities**: LP payoff is maximized as one token becomes dominant
3. **Impermanent Loss**: Occurs when probability deviates significantly from initial state
4. **Risk-Return Profile**: LPs earn higher returns in volatile probability environments

### Market Dynamics

- **Price Discovery**: Token prices automatically adjust based on probability updates
- **Arbitrage Opportunities**: Price discrepancies create trading opportunities
- **Liquidity Efficiency**: Concentrated liquidity around current probability

## 🚀 Deployment

1. Deploy YES and NO token contracts
2. Deploy the PredictionMarket contract with initial parameters
3. Add initial liquidity
4. Update probability as market conditions change
5. Resolve market when outcome is determined

## 📋 Dependencies

### Solidity
- OpenZeppelin Contracts (ERC20, ReentrancyGuard, Ownable)
- Forge/Foundry for testing

### Python
- numpy
- matplotlib
- seaborn
- scipy

## 🔮 Future Enhancements

- **Multi-outcome Markets**: Extend to more than binary outcomes
- **Automated Market Making**: Dynamic probability updates based on trading
- **Fee Mechanisms**: Implement trading fees for LPs
- **Oracle Integration**: Connect to external probability feeds
- **Governance**: Decentralized parameter updates

## 📄 License

MIT License - see LICENSE file for details

## 🤝 Contributing

Contributions welcome! Please read the contributing guidelines and submit pull requests.

---

*This implementation demonstrates advanced AMM mechanics with probability weighting, providing a foundation for sophisticated prediction markets.*