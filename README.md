# Prediction Market with Probability-Weighted AMM

A comprehensive implementation of a prediction market using the invariant **X^P * Y^(1-P) = k**, where P is the probability of the YES outcome.

## 🎯 Overview

This project implements a novel prediction market mechanism that generalizes the constant product AMM (X * Y = k) by incorporating probability weighting. When P = 0.5, it reduces to the familiar constant product formula.

## 📊 Key Features

- **Probability-Weighted Invariant**: X^P * Y^(1-P) = k
- **Dynamic Probability Updates**: Market probability can be updated by the owner
- **Liquidity Provision**: Users can provide liquidity and earn fees
- **Trading Mechanism**: Swap between YES and NO tokens
- **Market Resolution**: Resolve markets and claim winnings
- **LP Payoff Analysis**: Mathematical analysis and visualization of LP returns

## 🧮 Mathematical Foundation

### Invariant Formula
```
X^P * Y^(1-P) = k
```

Where:
- **X** = YES token reserves
- **Y** = NO token reserves
- **P** = Probability of YES outcome (0 ≤ P ≤ 1)
- **k** = Invariant constant

### LP Payoff Formula (Closed-Form Solution)

**Expected LP Payoff = P * X(P) + (1-P) * Y(P)**

Where X(P) and Y(P) are the equilibrium reserves at probability P satisfying the invariant constraint.

#### Key Properties:
1. **At P = 0.5**: Reduces to constant product AMM behavior
2. **At P → 0**: LP payoff approaches Y reserves (NO tokens)
3. **At P → 1**: LP payoff approaches X reserves (YES tokens)
4. **Minimum payoff**: Typically occurs around P = 0.5
5. **Maximum payoff**: Occurs at extreme probabilities (P → 0 or P → 1)

## 📁 Project Structure

```
├── PredictionMarket.sol          # Main Solidity contract
├── PredictionMarketTest.sol      # Comprehensive test suite
├── lp_payoff_visualization.py    # Python visualization script
├── LP_Payoff_Analysis.md         # Mathematical analysis
├── README.md                     # This file
└── Generated Visualizations:
    ├── lp_payoff_vs_probability.png
    ├── reserves_vs_probability.png
    ├── impermanent_loss_analysis.png
    └── combined_analysis.png
```

## 🔧 Smart Contract Features

### Core Functions

- `addLiquidity(uint256 yesAmount, uint256 noAmount)` - Add liquidity to the pool
- `removeLiquidity(uint256 shares)` - Remove liquidity from the pool
- `trade(bool buyYes, uint256 amountIn, uint256 minAmountOut)` - Trade tokens
- `updateProbability(uint256 newProbability)` - Update market probability (owner only)
- `resolveMarket(bool yesWins)` - Resolve the market (owner only)
- `claimWinnings()` - Claim winnings after resolution

### View Functions

- `getYesAmountOut(uint256 noAmountIn)` - Calculate YES tokens received for NO tokens
- `getNoAmountOut(uint256 yesAmountIn)` - Calculate NO tokens received for YES tokens
- `calculateLPPayoff(uint256 p, uint256 lpShares)` - Calculate expected LP payoff
- `getMarketState()` - Get current market state

## 📈 Visualizations

The project includes comprehensive visualizations showing:

1. **LP Payoff vs Probability**: How LP returns vary with market probability
2. **Token Reserves vs Probability**: How reserves change as probability shifts
3. **Impermanent Loss Analysis**: LP losses relative to holding tokens
4. **Combined Analysis**: Multi-panel view of all metrics

### Running Visualizations

```bash
python3 lp_payoff_visualization.py
```

## 🧪 Testing

Comprehensive test suite covering:

- Initial liquidity provision
- Proportional liquidity addition
- Token trading (both directions)
- Slippage protection
- Probability updates
- Market resolution
- Winnings claims
- Invariant maintenance

### Running Tests

```bash
forge test
```

## 🔍 Key Insights

### LP Payoff Behavior

1. **Symmetric Markets (P ≈ 0.5)**: LP payoff is minimized, similar to traditional AMMs
2. **Extreme Probabilities**: LP payoff is maximized as one token becomes dominant
3. **Impermanent Loss**: Occurs when probability deviates significantly from initial state
4. **Risk-Return Profile**: LPs earn higher returns in volatile probability environments

### Market Dynamics

- **Price Discovery**: Token prices automatically adjust based on probability updates
- **Arbitrage Opportunities**: Price discrepancies create trading opportunities
- **Liquidity Efficiency**: Concentrated liquidity around current probability

## 🚀 Deployment

1. Deploy YES and NO token contracts
2. Deploy the PredictionMarket contract with initial parameters
3. Add initial liquidity
4. Update probability as market conditions change
5. Resolve market when outcome is determined

## 📋 Dependencies

### Solidity
- OpenZeppelin Contracts (ERC20, ReentrancyGuard, Ownable)
- Forge/Foundry for testing

### Python
- numpy
- matplotlib
- seaborn
- scipy

## 🔮 Future Enhancements

- **Multi-outcome Markets**: Extend to more than binary outcomes
- **Automated Market Making**: Dynamic probability updates based on trading
- **Fee Mechanisms**: Implement trading fees for LPs
- **Oracle Integration**: Connect to external probability feeds
- **Governance**: Decentralized parameter updates

## 📄 License

MIT License - see LICENSE file for details

## 🤝 Contributing

Contributions welcome! Please read the contributing guidelines and submit pull requests.

---

*This implementation demonstrates advanced AMM mechanics with probability weighting, providing a foundation for sophisticated prediction markets.*