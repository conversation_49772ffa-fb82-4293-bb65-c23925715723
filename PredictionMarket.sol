// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title PredictionMarket
 * @dev A prediction market using the invariant X^P * Y^(1-P) = k
 * where X = YES shares, Y = NO shares, P = probability of YES outcome
 */
contract PredictionMarket is ReentrancyGuard, Ownable {

    // Share tokens
    IERC20 public immutable yesToken;
    IERC20 public immutable noToken;
    IERC20 public immutable collateralToken;

    // Market state
    uint256 public yesReserves;
    uint256 public noReserves;
    uint256 public totalLiquidity;
    uint256 public constant PRECISION = 1e18;

    // Market parameters
    uint256 public probability; // Probability * PRECISION (0 to PRECISION)
    uint256 public k; // Invariant constant
    bool public resolved;
    bool public yesWins;

    // LP tracking
    mapping(address => uint256) public liquidityShares;

    // Events
    event LiquidityAdded(address indexed provider, uint256 yesAmount, uint256 noAmount, uint256 shares);
    event LiquidityRemoved(address indexed provider, uint256 yesAmount, uint256 noAmount, uint256 shares);
    event Trade(address indexed trader, bool buyYes, uint256 amountIn, uint256 amountOut);
    event MarketResolved(bool yesWins);
    event ProbabilityUpdated(uint256 newProbability);

    constructor(
        address _yesToken,
        address _noToken,
        address _collateralToken,
        uint256 _initialProbability
    ) {
        yesToken = IERC20(_yesToken);
        noToken = IERC20(_noToken);
        collateralToken = IERC20(_collateralToken);
        probability = _initialProbability;
        require(_initialProbability <= PRECISION, "Invalid probability");
    }

    /**
     * @dev Add liquidity to the pool
     * @param yesAmount Amount of YES tokens to add
     * @param noAmount Amount of NO tokens to add
     */
    function addLiquidity(uint256 yesAmount, uint256 noAmount) external nonReentrant {
        require(!resolved, "Market resolved");
        require(yesAmount > 0 && noAmount > 0, "Invalid amounts");

        // Transfer tokens
        yesToken.transferFrom(msg.sender, address(this), yesAmount);
        noToken.transferFrom(msg.sender, address(this), noAmount);

        uint256 shares;
        if (totalLiquidity == 0) {
            // Initial liquidity
            shares = sqrt(yesAmount * noAmount);
            yesReserves = yesAmount;
            noReserves = noAmount;
            k = calculateK(yesReserves, noReserves);
        } else {
            // Proportional liquidity addition
            uint256 yesRatio = (yesAmount * PRECISION) / yesReserves;
            uint256 noRatio = (noAmount * PRECISION) / noReserves;
            require(yesRatio == noRatio, "Must maintain ratio");

            shares = (totalLiquidity * yesRatio) / PRECISION;
            yesReserves += yesAmount;
            noReserves += noAmount;
        }

        liquidityShares[msg.sender] += shares;
        totalLiquidity += shares;

        emit LiquidityAdded(msg.sender, yesAmount, noAmount, shares);
    }

    /**
     * @dev Remove liquidity from the pool
     * @param shares Amount of liquidity shares to remove
     */
    function removeLiquidity(uint256 shares) external nonReentrant {
        require(shares > 0, "Invalid shares");
        require(liquidityShares[msg.sender] >= shares, "Insufficient shares");

        uint256 yesAmount = (yesReserves * shares) / totalLiquidity;
        uint256 noAmount = (noReserves * shares) / totalLiquidity;

        liquidityShares[msg.sender] -= shares;
        totalLiquidity -= shares;
        yesReserves -= yesAmount;
        noReserves -= noAmount;

        yesToken.transfer(msg.sender, yesAmount);
        noToken.transfer(msg.sender, noAmount);

        emit LiquidityRemoved(msg.sender, yesAmount, noAmount, shares);
    }

    /**
     * @dev Trade YES tokens for NO tokens or vice versa
     * @param buyYes True to buy YES tokens, false to buy NO tokens
     * @param amountIn Amount of tokens to trade in
     * @param minAmountOut Minimum amount of tokens to receive
     */
    function trade(bool buyYes, uint256 amountIn, uint256 minAmountOut) external nonReentrant {
        require(!resolved, "Market resolved");
        require(amountIn > 0, "Invalid amount");

        uint256 amountOut;
        if (buyYes) {
            // Buy YES tokens with NO tokens
            noToken.transferFrom(msg.sender, address(this), amountIn);
            amountOut = getYesAmountOut(amountIn);
            require(amountOut >= minAmountOut, "Slippage exceeded");

            noReserves += amountIn;
            yesReserves -= amountOut;
            yesToken.transfer(msg.sender, amountOut);
        } else {
            // Buy NO tokens with YES tokens
            yesToken.transferFrom(msg.sender, address(this), amountIn);
            amountOut = getNoAmountOut(amountIn);
            require(amountOut >= minAmountOut, "Slippage exceeded");

            yesReserves += amountIn;
            noReserves -= amountOut;
            noToken.transfer(msg.sender, amountOut);
        }

        emit Trade(msg.sender, buyYes, amountIn, amountOut);
    }

    /**
     * @dev Calculate amount of YES tokens received for NO tokens
     * @param noAmountIn Amount of NO tokens to trade
     * @return Amount of YES tokens to receive
     */
    function getYesAmountOut(uint256 noAmountIn) public view returns (uint256) {
        if (noAmountIn == 0) return 0;

        uint256 newNoReserves = noReserves + noAmountIn;
        uint256 newYesReserves = calculateYesFromNo(newNoReserves);

        return yesReserves - newYesReserves;
    }

    /**
     * @dev Calculate amount of NO tokens received for YES tokens
     * @param yesAmountIn Amount of YES tokens to trade
     * @return Amount of NO tokens to receive
     */
    function getNoAmountOut(uint256 yesAmountIn) public view returns (uint256) {
        if (yesAmountIn == 0) return 0;

        uint256 newYesReserves = yesReserves + yesAmountIn;
        uint256 newNoReserves = calculateNoFromYes(newYesReserves);

        return noReserves - newNoReserves;
    }

    /**
     * @dev Calculate YES reserves from NO reserves using invariant
     * @param noAmount NO token reserves
     * @return YES token reserves
     */
    function calculateYesFromNo(uint256 noAmount) internal view returns (uint256) {
        if (probability == 0) return type(uint256).max;
        if (probability == PRECISION) return k;

        // Y = (k / X^P)^(1/(1-P))
        // X = (k / Y^(1-P))^(1/P)
        uint256 oneMinusP = PRECISION - probability;
        uint256 yPower = pow(noAmount, oneMinusP, PRECISION);
        uint256 kDivYPower = (k * PRECISION) / yPower;

        return pow(kDivYPower, PRECISION, probability);
    }

    /**
     * @dev Calculate NO reserves from YES reserves using invariant
     * @param yesAmount YES token reserves
     * @return NO token reserves
     */
    function calculateNoFromYes(uint256 yesAmount) internal view returns (uint256) {
        if (probability == PRECISION) return type(uint256).max;
        if (probability == 0) return k;

        // Y = (k / X^P)^(1/(1-P))
        uint256 oneMinusP = PRECISION - probability;
        uint256 xPower = pow(yesAmount, probability, PRECISION);
        uint256 kDivXPower = (k * PRECISION) / xPower;

        return pow(kDivXPower, PRECISION, oneMinusP);
    }

    /**
     * @dev Calculate the invariant constant k
     * @param x YES reserves
     * @param y NO reserves
     * @return The invariant constant
     */
    function calculateK(uint256 x, uint256 y) internal view returns (uint256) {
        if (probability == 0) return y;
        if (probability == PRECISION) return x;

        uint256 xPower = pow(x, probability, PRECISION);
        uint256 yPower = pow(y, PRECISION - probability, PRECISION);

        return (xPower * yPower) / PRECISION;
    }

    /**
     * @dev Update the probability (only owner)
     * @param newProbability New probability value
     */
    function updateProbability(uint256 newProbability) external onlyOwner {
        require(!resolved, "Market resolved");
        require(newProbability <= PRECISION, "Invalid probability");

        probability = newProbability;
        k = calculateK(yesReserves, noReserves);

        emit ProbabilityUpdated(newProbability);
    }

    /**
     * @dev Resolve the market (only owner)
     * @param _yesWins True if YES wins, false if NO wins
     */
    function resolveMarket(bool _yesWins) external onlyOwner {
        require(!resolved, "Already resolved");

        resolved = true;
        yesWins = _yesWins;

        emit MarketResolved(_yesWins);
    }

    /**
     * @dev Claim winnings after market resolution
     */
    function claimWinnings() external nonReentrant {
        require(resolved, "Market not resolved");
        require(liquidityShares[msg.sender] > 0, "No liquidity shares");

        uint256 shares = liquidityShares[msg.sender];
        liquidityShares[msg.sender] = 0;

        uint256 payout;
        if (yesWins) {
            payout = (yesReserves * shares) / totalLiquidity;
            yesToken.transfer(msg.sender, payout);
        } else {
            payout = (noReserves * shares) / totalLiquidity;
            noToken.transfer(msg.sender, payout);
        }
    }

    /**
     * @dev Calculate LP payoff for a given probability
     * @param p Probability (scaled by PRECISION)
     * @param lpShares LP's share of total liquidity
     * @return Expected payoff
     */
    function calculateLPPayoff(uint256 p, uint256 lpShares) external view returns (uint256) {
        if (totalLiquidity == 0) return 0;

        uint256 lpYesReserves = (yesReserves * lpShares) / totalLiquidity;
        uint256 lpNoReserves = (noReserves * lpShares) / totalLiquidity;

        // Expected payoff = P * X + (1-P) * Y
        uint256 yesComponent = (p * lpYesReserves) / PRECISION;
        uint256 noComponent = ((PRECISION - p) * lpNoReserves) / PRECISION;

        return yesComponent + noComponent;
    }

    /**
     * @dev Get current market state
     */
    function getMarketState() external view returns (
        uint256 _yesReserves,
        uint256 _noReserves,
        uint256 _probability,
        uint256 _k,
        uint256 _totalLiquidity,
        bool _resolved,
        bool _yesWins
    ) {
        return (yesReserves, noReserves, probability, k, totalLiquidity, resolved, yesWins);
    }

    // Utility functions
    function sqrt(uint256 x) internal pure returns (uint256) {
        if (x == 0) return 0;
        uint256 z = (x + 1) / 2;
        uint256 y = x;
        while (z < y) {
            y = z;
            z = (x / z + z) / 2;
        }
        return y;
    }

    /**
     * @dev Calculate x^y with precision scaling
     * @param base Base value
     * @param exponent Exponent value
     * @param precision Precision scaling factor
     * @return Result of base^exponent
     */
    function pow(uint256 base, uint256 exponent, uint256 precision) internal pure returns (uint256) {
        if (exponent == 0) return precision;
        if (base == 0) return 0;
        if (exponent == precision) return base;

        // For simplicity, using approximation for fractional powers
        // In production, use a more sophisticated power function library
        uint256 result = precision;
        uint256 baseAccumulator = base;
        uint256 exp = exponent;

        while (exp > 0) {
            if (exp % 2 == 1) {
                result = (result * baseAccumulator) / precision;
            }
            baseAccumulator = (baseAccumulator * baseAccumulator) / precision;
            exp /= 2;
        }

        return result;
    }
}