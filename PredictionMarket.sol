// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title PredictionMarket
 * @dev A prediction market using the invariant X^(P_yes) * Y^(1-P_yes) = K
 * Derived from LP payoff function: V = x^(1-P_yes) * (1-x)^(P_yes)
 * where X = YES reserves, Y = NO reserves, P_yes = probability of YES outcome
 */
contract PredictionMarket is ERC20, ReentrancyGuard, Ownable {

    // Share tokens
    IERC20 public immutable yesToken;
    IERC20 public immutable noToken;

    // Market state
    uint256 public yesReserves;
    uint256 public noReserves;
    uint256 public constant PRECISION = 1e18;
    uint256 public constant MIN_RESERVE = 1e12; // Minimum reserve to prevent extreme imbalance

    // Market parameters
    uint256 public pYes; // Probability of YES * PRECISION (0 to PRECISION)
    uint256 public K; // Invariant constant: X^(P_yes) * Y^(1-P_yes) = K
    bool public resolved;
    bool public yesWins;

    // Events
    event LiquidityMinted(address indexed to, uint256 yesIn, uint256 noIn, uint256 liquidity);
    event LiquidityBurned(address indexed from, uint256 yesOut, uint256 noOut, uint256 liquidity);
    event YesForNoSwap(address indexed to, uint256 yesIn, uint256 noOut, uint256 newPYes);
    event NoForYesSwap(address indexed to, uint256 noIn, uint256 yesOut, uint256 newPYes);
    event ProbabilityUpdated(uint256 newProbability);
    event MarketResolved(bool yesWins);
    event LPPayoffCalculated(uint256 lpPayoff, uint256 currentPYes);


    constructor(
        address _yesToken,
        address _noToken,
        uint256 _initialPYes,
        string memory _name,
        string memory _symbol
    ) ERC20(_name, _symbol) {
        yesToken = IERC20(_yesToken);
        noToken = IERC20(_noToken);
        pYes = _initialPYes;
        require(_initialPYes <= PRECISION, "Invalid probability");
    }

    /**
     * @dev Get user's deposited YES tokens (placeholder - implement based on your deposit mechanism)
     */
    function getYesDepositedByUser() internal view returns (uint256) {
        // This should be implemented based on how users deposit tokens
        // For now, returning 0 as placeholder
        return 0;
    }

    /**
     * @dev Get user's deposited NO tokens (placeholder - implement based on your deposit mechanism)
     */
    function getNoDepositedByUser() internal view returns (uint256) {
        // This should be implemented based on how users deposit tokens
        // For now, returning 0 as placeholder
        return 0;
    }

    /**
     * @dev Get current reserves
     */
    function getReserves() public view returns (uint256, uint256) {
        return (yesReserves, noReserves);
    }

    /**
     * @dev Mint LP tokens - maintains optimal reserve ratio
     * @param to Address to mint LP tokens to
     * @param yesIn Amount of YES tokens to deposit
     * @param noIn Amount of NO tokens to deposit
     */
    function mintLP(address to, uint256 yesIn, uint256 noIn) external nonReentrant {
        require(!resolved, "Market resolved");
        require(yesIn > 0 && noIn > 0, "Invalid amounts");

        (uint256 yesReserve, uint256 noReserve) = getReserves();

        if (totalSupply() == 0) {
            // Initial liquidity
            yesToken.transferFrom(msg.sender, address(this), yesIn);
            noToken.transferFrom(msg.sender, address(this), noIn);

            yesReserves = yesIn;
            noReserves = noIn;
            K = calculateInvariant(yesReserves, noReserves);

            // Initial LP tokens based on geometric mean
            uint256 liquidity = sqrt(yesIn * noIn);
            _mint(to, liquidity);

            emit LiquidityMinted(to, yesIn, noIn, liquidity);
        } else {
            // Check optimal ratio: yesIn/noIn should equal (1-pYes)/pYes
            // Rearranged: yesIn * pYes == noIn * (1-pYes)
            uint256 leftSide = yesIn * pYes;
            uint256 rightSide = noIn * (PRECISION - pYes);
            require(leftSide == rightSide, "UNBALANCED_DEPOSIT");

            // Transfer tokens
            yesToken.transferFrom(msg.sender, address(this), yesIn);
            noToken.transferFrom(msg.sender, address(this), noIn);

            // Calculate LP tokens proportional to reserve increase
            uint256 yesRatio = (yesIn * PRECISION) / yesReserve;
            uint256 noRatio = (noIn * PRECISION) / noReserve;
            require(yesRatio == noRatio, "PROPORTIONAL_ERROR");

            uint256 liquidity = (totalSupply() * yesRatio) / PRECISION;
            _mint(to, liquidity);

            _updateReserves(yesReserve + yesIn, noReserve + noIn);

            emit LiquidityMinted(to, yesIn, noIn, liquidity);
        }
    }

    /**
     * @dev Burn LP tokens and withdraw proportional reserves
     * @param liquidity Amount of LP tokens to burn
     */
    function burnLP(uint256 liquidity) external nonReentrant {
        require(liquidity > 0, "Invalid liquidity");
        require(balanceOf(msg.sender) >= liquidity, "Insufficient LP tokens");

        (uint256 yesReserve, uint256 noReserve) = getReserves();

        uint256 yesOut = (yesReserve * liquidity) / totalSupply();
        uint256 noOut = (noReserve * liquidity) / totalSupply();

        _burn(msg.sender, liquidity);
        _updateReserves(yesReserve - yesOut, noReserve - noOut);

        yesToken.transfer(msg.sender, yesOut);
        noToken.transfer(msg.sender, noOut);

        emit LiquidityBurned(msg.sender, yesOut, noOut, liquidity);
    }

    /**
     * @dev Swap YES tokens for NO tokens using invariant X^(pYes) * Y^(1-pYes) = K
     * @param yesIn Amount of YES tokens to swap
     * @param to Address to receive NO tokens
     */
    function swapYesForNo(uint256 yesIn, address to) external nonReentrant {
        require(!resolved, "Market resolved");
        require(yesIn > 0, "Invalid input");

        (uint256 yesReserve, uint256 noReserve) = getReserves();

        // Calculate new NO reserves using invariant: X^(pYes) * Y^(1-pYes) = K
        uint256 newYes = yesReserve + yesIn;
        uint256 newNo = calculateNoFromYes(newYes);
        uint256 noOut = noReserve - newNo;

        // Enforce constraints
        require(noOut > 0, "INSUFFICIENT_OUTPUT");
        require(newNo > MIN_RESERVE, "EXTREME_IMBALANCE");

        // Transfer tokens
        yesToken.transferFrom(msg.sender, address(this), yesIn);
        noToken.transfer(to, noOut);

        _updateReserves(newYes, newNo);

        // Emit swap event with updated probability
        emit YesForNoSwap(to, yesIn, noOut, pYes);
    }

    /**
     * @dev Swap NO tokens for YES tokens using invariant X^(pYes) * Y^(1-pYes) = K
     * @param noIn Amount of NO tokens to swap
     * @param to Address to receive YES tokens
     */
    function swapNoForYes(uint256 noIn, address to) external nonReentrant {
        require(!resolved, "Market resolved");
        require(noIn > 0, "Invalid input");

        (uint256 yesReserve, uint256 noReserve) = getReserves();

        // Calculate new YES reserves using invariant: X^(pYes) * Y^(1-pYes) = K
        uint256 newNo = noReserve + noIn;
        uint256 newYes = calculateYesFromNo(newNo);
        uint256 yesOut = yesReserve - newYes;

        // Enforce constraints
        require(yesOut > 0, "INSUFFICIENT_OUTPUT");
        require(newYes > MIN_RESERVE, "EXTREME_IMBALANCE");

        // Transfer tokens
        noToken.transferFrom(msg.sender, address(this), noIn);
        yesToken.transfer(to, yesOut);

        _updateReserves(newYes, newNo);

        // Emit swap event with updated probability
        emit NoForYesSwap(to, noIn, yesOut, pYes);
    }

    /**
     * @dev Calculate NO reserves from YES reserves using invariant X^(pYes) * Y^(1-pYes) = K
     * @param yesAmount YES token reserves
     * @return NO token reserves
     */
    function calculateNoFromYes(uint256 yesAmount) internal view returns (uint256) {
        if (pYes == PRECISION) return K; // Edge case: pYes = 1
        if (pYes == 0) return type(uint256).max; // Edge case: pYes = 0

        // From X^(pYes) * Y^(1-pYes) = K, solve for Y: Y = (K / X^(pYes))^(1/(1-pYes))
        uint256 oneMinusPYes = PRECISION - pYes;
        uint256 xPower = pow(yesAmount, pYes, PRECISION);
        uint256 kDivXPower = (K * PRECISION) / xPower;

        return pow(kDivXPower, PRECISION, oneMinusPYes);
    }

    /**
     * @dev Calculate YES reserves from NO reserves using invariant X^(pYes) * Y^(1-pYes) = K
     * @param noAmount NO token reserves
     * @return YES token reserves
     */
    function calculateYesFromNo(uint256 noAmount) internal view returns (uint256) {
        if (pYes == 0) return K; // Edge case: pYes = 0
        if (pYes == PRECISION) return type(uint256).max; // Edge case: pYes = 1

        // From X^(pYes) * Y^(1-pYes) = K, solve for X: X = (K / Y^(1-pYes))^(1/pYes)
        uint256 oneMinusPYes = PRECISION - pYes;
        uint256 yPower = pow(noAmount, oneMinusPYes, PRECISION);
        uint256 kDivYPower = (K * PRECISION) / yPower;

        return pow(kDivYPower, PRECISION, pYes);
    }

    /**
     * @dev Calculate the invariant constant K = X^(pYes) * Y^(1-pYes)
     * @param x YES reserves
     * @param y NO reserves
     * @return The invariant constant
     */
    function calculateInvariant(uint256 x, uint256 y) internal view returns (uint256) {
        if (pYes == 0) return y;
        if (pYes == PRECISION) return x;

        uint256 xPower = pow(x, pYes, PRECISION);
        uint256 yPower = pow(y, PRECISION - pYes, PRECISION);

        return (xPower * yPower) / PRECISION;
    }

    /**
     * @dev Calculate LP payoff: V = x^(1-pYes) * (1-x)^(pYes) * totalReserves
     * @param lpShares LP's share of total liquidity
     * @return LP payoff value
     */
    function calculateLPPayoff(uint256 lpShares) external view returns (uint256) {
        if (totalSupply() == 0) return 0;

        uint256 totalReserves = yesReserves + noReserves;
        uint256 lpTotalReserves = (totalReserves * lpShares) / totalSupply();

        if (totalReserves == 0) return 0;

        // Calculate x = yesReserves / totalReserves
        uint256 x = (yesReserves * PRECISION) / totalReserves;
        uint256 oneMinusX = PRECISION - x;

        // V = x^(1-pYes) * (1-x)^(pYes) * totalReserves
        uint256 xPower = pow(x, PRECISION - pYes, PRECISION);
        uint256 oneMinusXPower = pow(oneMinusX, pYes, PRECISION);
        uint256 payoffRatio = (xPower * oneMinusXPower) / PRECISION;

        uint256 lpPayoff = (lpTotalReserves * payoffRatio) / PRECISION;

        emit LPPayoffCalculated(lpPayoff, pYes);
        return lpPayoff;
    }

    /**
     * @dev Update reserves and invariant
     * @param newYes New YES reserves
     * @param newNo New NO reserves
     */
    function _updateReserves(uint256 newYes, uint256 newNo) internal {
        yesReserves = newYes;
        noReserves = newNo;
        K = calculateInvariant(newYes, newNo);
    }

    /**
     * @dev Calculate the invariant constant k
     * @param x YES reserves
     * @param y NO reserves
     * @return The invariant constant
     */
    function calculateK(uint256 x, uint256 y) internal view returns (uint256) {
        if (probability == 0) return y;
        if (probability == PRECISION) return x;

        uint256 xPower = pow(x, probability, PRECISION);
        uint256 yPower = pow(y, PRECISION - probability, PRECISION);

        return (xPower * yPower) / PRECISION;
    }

    /**
     * @dev Update the probability (only owner)
     * @param newProbability New probability value
     */
    function updateProbability(uint256 newProbability) external onlyOwner {
        require(!resolved, "Market resolved");
        require(newProbability <= PRECISION, "Invalid probability");

        probability = newProbability;
        k = calculateK(yesReserves, noReserves);

        emit ProbabilityUpdated(newProbability);
    }

    /**
     * @dev Resolve the market (only owner)
     * @param _yesWins True if YES wins, false if NO wins
     */
    function resolveMarket(bool _yesWins) external onlyOwner {
        require(!resolved, "Already resolved");

        resolved = true;
        yesWins = _yesWins;

        emit MarketResolved(_yesWins);
    }

    /**
     * @dev Claim winnings after market resolution
     */
    function claimWinnings() external nonReentrant {
        require(resolved, "Market not resolved");
        require(liquidityShares[msg.sender] > 0, "No liquidity shares");

        uint256 shares = liquidityShares[msg.sender];
        liquidityShares[msg.sender] = 0;

        uint256 payout;
        if (yesWins) {
            payout = (yesReserves * shares) / totalLiquidity;
            yesToken.transfer(msg.sender, payout);
        } else {
            payout = (noReserves * shares) / totalLiquidity;
            noToken.transfer(msg.sender, payout);
        }
    }

    /**
     * @dev Calculate LP payoff for a given probability
     * @param p Probability (scaled by PRECISION)
     * @param lpShares LP's share of total liquidity
     * @return Expected payoff
     */
    function calculateLPPayoff(uint256 p, uint256 lpShares) external view returns (uint256) {
        if (totalLiquidity == 0) return 0;

        uint256 lpYesReserves = (yesReserves * lpShares) / totalLiquidity;
        uint256 lpNoReserves = (noReserves * lpShares) / totalLiquidity;

        // Expected payoff = P * X + (1-P) * Y
        uint256 yesComponent = (p * lpYesReserves) / PRECISION;
        uint256 noComponent = ((PRECISION - p) * lpNoReserves) / PRECISION;

        return yesComponent + noComponent;
    }

    /**
     * @dev Get current market state
     */
    function getMarketState() external view returns (
        uint256 _yesReserves,
        uint256 _noReserves,
        uint256 _probability,
        uint256 _k,
        uint256 _totalLiquidity,
        bool _resolved,
        bool _yesWins
    ) {
        return (yesReserves, noReserves, probability, k, totalLiquidity, resolved, yesWins);
    }

    // Utility functions
    function sqrt(uint256 x) internal pure returns (uint256) {
        if (x == 0) return 0;
        uint256 z = (x + 1) / 2;
        uint256 y = x;
        while (z < y) {
            y = z;
            z = (x / z + z) / 2;
        }
        return y;
    }

    /**
     * @dev Calculate x^y with precision scaling
     * @param base Base value
     * @param exponent Exponent value
     * @param precision Precision scaling factor
     * @return Result of base^exponent
     */
    function pow(uint256 base, uint256 exponent, uint256 precision) internal pure returns (uint256) {
        if (exponent == 0) return precision;
        if (base == 0) return 0;
        if (exponent == precision) return base;

        // For simplicity, using approximation for fractional powers
        // In production, use a more sophisticated power function library
        uint256 result = precision;
        uint256 baseAccumulator = base;
        uint256 exp = exponent;

        while (exp > 0) {
            if (exp % 2 == 1) {
                result = (result * baseAccumulator) / precision;
            }
            baseAccumulator = (baseAccumulator * baseAccumulator) / precision;
            exp /= 2;
        }

        return result;
    }
}