# LP Payoff Analysis for Prediction Market

## Given LP Payoff Function
**V(x, P_yes) = x^(1-P_yes) * (1-x)^(P_yes)**

Where:
- V = LP Payoff value
- x = fraction of YES tokens in portfolio (0 ≤ x ≤ 1)
- P_yes = probability of YES outcome (0 ≤ P_yes ≤ 1)

This is a concave function where the peak occurs at x = 1 - P_yes.

## Deriving the Invariant via Legendre Transform

### Step 1: Find the Optimal Portfolio Allocation
To find the maximum of V(x, P_yes), we take the derivative with respect to x:

**∂V/∂x = (1-P_yes) * x^(-P_yes) * (1-x)^(P_yes) - P_yes * x^(1-P_yes) * (1-x)^(P_yes-1)**

Setting ∂V/∂x = 0 and solving:
**(1-P_yes) * (1-x) = P_yes * x**

This gives us the optimal allocation: **x* = 1 - P_yes**

### Step 2: Calculate the Maximum LP Payoff
Substituting x* = 1 - P_yes into the LP payoff function:

**V_max = (1-P_yes)^(1-P_yes) * (P_yes)^(P_yes)**

### Step 3: Legendre Transform to Derive Invariant
The Legendre transform of the LP payoff function gives us the invariant.

For a function V(x), the Legendre transform is:
**L(p) = max_x [px - V(x)]**

In our context, if we define:
- X = total YES reserves
- Y = total NO reserves
- x = X/(X+Y) (fraction of YES tokens)

The invariant becomes:
**X^(P_yes) * Y^(1-P_yes) = K**

Where K is the invariant constant.

### Step 4: Verification
The LP payoff in terms of reserves becomes:
**V = (X/(X+Y))^(1-P_yes) * (Y/(X+Y))^(P_yes) * (X+Y)**

Simplifying:
**V = X^(1-P_yes) * Y^(P_yes) / (X+Y)^(1-1) = X^(1-P_yes) * Y^(P_yes)**

## Mathematical Properties

### Optimal Reserve Ratio
At equilibrium, the optimal ratio is:
**X/Y = (1-P_yes)/P_yes**

### LP Payoff Formula
For an LP with total liquidity L:
**LP_Payoff = L * (X/L)^(1-P_yes) * (Y/L)^(P_yes)**

Where X + Y = L (total reserves).

### Invariant Constraint
The market maintains: **X^(P_yes) * Y^(1-P_yes) = K**

Note: This is the **inverse** of the original X^P * Y^(1-P) formulation, reflecting the Legendre transform relationship.

## Key Insights
1. **Peak LP payoff** occurs when reserve ratio matches probability: X/Y = (1-P_yes)/P_yes
2. **Concave payoff structure** ensures unique maximum
3. **Invariant preserves** the optimal allocation relationship
4. **Market efficiency** is achieved when reserves align with probabilities