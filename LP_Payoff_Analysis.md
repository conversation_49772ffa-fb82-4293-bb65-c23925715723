# LP Payoff Analysis for Prediction Market

## Invariant
X^P * Y^(1-P) = k

Where:
- X = reserves of YES shares
- Y = reserves of NO shares
- P = probability of YES outcome (0 ≤ P ≤ 1)
- k = constant

## LP Payoff Derivation

### Expected Payoff Formula
For an LP with share α of the total liquidity:

**Expected LP Payoff = P * (α * X) + (1-P) * (α * Y)**

### Closed-Form Solution
From the invariant constraint X^P * Y^(1-P) = k, we can express Y in terms of X:

Y = (k/X^P)^(1/(1-P))

For a normalized case where we set k = 1 and consider equal initial reserves X₀ = Y₀:
- Initial condition: X₀^P * Y₀^(1-P) = 1
- If X₀ = Y₀, then X₀ = Y₀ = 1

### Equilibrium Analysis
At equilibrium, the marginal rates must satisfy the probability constraint.

The marginal rate of substitution from the invariant:
dY/dX = -P/(1-P) * Y/X

### LP Payoff Function
For a unit liquidity provider (α = 1):

**LP_Payoff(P) = P * X(P) + (1-P) * Y(P)**

Where X(P) and Y(P) are the equilibrium reserves at probability P.

### Special Cases
1. **P = 0.5 (Equal probability):**
   - Reduces to constant product: X^0.5 * Y^0.5 = k
   - LP_Payoff = 0.5 * X + 0.5 * Y

2. **P → 0 (NO almost certain):**
   - X^0 * Y^1 = k → Y = k
   - LP_Payoff ≈ Y = k

3. **P → 1 (YES almost certain):**
   - X^1 * Y^0 = k → X = k
   - LP_Payoff ≈ X = k

### Impermanent Loss Analysis
The LP faces impermanent loss when the probability deviates from 0.5, as the reserves become imbalanced according to the probability-weighted invariant.

## Key Properties
1. The LP payoff is always bounded between the minimum and maximum of the initial reserves
2. Maximum payoff occurs at extreme probabilities (P → 0 or P → 1)
3. Minimum payoff typically occurs around P = 0.5 for symmetric initial conditions