// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "forge-std/Test.sol";
import "./PredictionMarket.sol";
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";

contract MockERC20 is ERC20 {
    constructor(string memory name, string memory symbol) ERC20(name, symbol) {
        _mint(msg.sender, 1000000 * 10**18);
    }

    function mint(address to, uint256 amount) external {
        _mint(to, amount);
    }
}

contract PredictionMarketTest is Test {
    PredictionMarket public market;
    MockERC20 public yesToken;
    MockERC20 public noToken;
    MockERC20 public collateralToken;

    address public owner = address(0x1);
    address public alice = address(0x2);
    address public bob = address(0x3);
    address public charlie = address(0x4);

    uint256 public constant PRECISION = 1e18;
    uint256 public constant INITIAL_PROBABILITY = PRECISION / 2; // 50%

    function setUp() public {
        vm.startPrank(owner);

        // Deploy mock tokens
        yesToken = new MockERC20("YES Token", "YES");
        noToken = new MockERC20("NO Token", "NO");
        collateralToken = new MockERC20("Collateral", "COLL");

        // Deploy prediction market
        market = new PredictionMarket(
            address(yesToken),
            address(noToken),
            address(collateralToken),
            INITIAL_PROBABILITY
        );

        // Mint tokens to test users
        yesToken.mint(alice, 1000 * PRECISION);
        noToken.mint(alice, 1000 * PRECISION);
        yesToken.mint(bob, 1000 * PRECISION);
        noToken.mint(bob, 1000 * PRECISION);
        yesToken.mint(charlie, 1000 * PRECISION);
        noToken.mint(charlie, 1000 * PRECISION);

        vm.stopPrank();
    }

    function testInitialState() public {
        assertEq(address(market.yesToken()), address(yesToken));
        assertEq(address(market.noToken()), address(noToken));
        assertEq(market.probability(), INITIAL_PROBABILITY);
        assertEq(market.yesReserves(), 0);
        assertEq(market.noReserves(), 0);
        assertEq(market.totalLiquidity(), 0);
        assertFalse(market.resolved());
    }

    function testAddInitialLiquidity() public {
        uint256 yesAmount = 100 * PRECISION;
        uint256 noAmount = 100 * PRECISION;

        vm.startPrank(alice);
        yesToken.approve(address(market), yesAmount);
        noToken.approve(address(market), noAmount);

        vm.expectEmit(true, false, false, true);
        emit PredictionMarket.LiquidityAdded(alice, yesAmount, noAmount, 100 * PRECISION);

        market.addLiquidity(yesAmount, noAmount);

        assertEq(market.yesReserves(), yesAmount);
        assertEq(market.noReserves(), noAmount);
        assertEq(market.liquidityShares(alice), 100 * PRECISION);
        assertEq(market.totalLiquidity(), 100 * PRECISION);
        assertGt(market.k(), 0);

        vm.stopPrank();
    }

    function testAddProportionalLiquidity() public {
        // Alice adds initial liquidity
        uint256 initialYes = 100 * PRECISION;
        uint256 initialNo = 100 * PRECISION;

        vm.startPrank(alice);
        yesToken.approve(address(market), initialYes);
        noToken.approve(address(market), initialNo);
        market.addLiquidity(initialYes, initialNo);
        vm.stopPrank();

        // Bob adds proportional liquidity
        uint256 bobYes = 50 * PRECISION;
        uint256 bobNo = 50 * PRECISION;

        vm.startPrank(bob);
        yesToken.approve(address(market), bobYes);
        noToken.approve(address(market), bobNo);
        market.addLiquidity(bobYes, bobNo);

        assertEq(market.yesReserves(), initialYes + bobYes);
        assertEq(market.noReserves(), initialNo + bobNo);
        assertEq(market.liquidityShares(bob), 50 * PRECISION);

        vm.stopPrank();
    }

    function testFailAddNonProportionalLiquidity() public {
        // Alice adds initial liquidity
        vm.startPrank(alice);
        yesToken.approve(address(market), 100 * PRECISION);
        noToken.approve(address(market), 100 * PRECISION);
        market.addLiquidity(100 * PRECISION, 100 * PRECISION);
        vm.stopPrank();

        // Bob tries to add non-proportional liquidity (should fail)
        vm.startPrank(bob);
        yesToken.approve(address(market), 50 * PRECISION);
        noToken.approve(address(market), 100 * PRECISION); // Different ratio
        market.addLiquidity(50 * PRECISION, 100 * PRECISION);
        vm.stopPrank();
    }

    function testRemoveLiquidity() public {
        // Alice adds liquidity
        uint256 yesAmount = 100 * PRECISION;
        uint256 noAmount = 100 * PRECISION;

        vm.startPrank(alice);
        yesToken.approve(address(market), yesAmount);
        noToken.approve(address(market), noAmount);
        market.addLiquidity(yesAmount, noAmount);

        uint256 sharesToRemove = 50 * PRECISION;
        uint256 aliceYesBefore = yesToken.balanceOf(alice);
        uint256 aliceNoBefore = noToken.balanceOf(alice);

        vm.expectEmit(true, false, false, true);
        emit PredictionMarket.LiquidityRemoved(alice, 50 * PRECISION, 50 * PRECISION, sharesToRemove);

        market.removeLiquidity(sharesToRemove);

        assertEq(market.liquidityShares(alice), 50 * PRECISION);
        assertEq(market.yesReserves(), 50 * PRECISION);
        assertEq(market.noReserves(), 50 * PRECISION);
        assertEq(yesToken.balanceOf(alice), aliceYesBefore + 50 * PRECISION);
        assertEq(noToken.balanceOf(alice), aliceNoBefore + 50 * PRECISION);

        vm.stopPrank();
    }

    function testTradeYesForNo() public {
        // Setup initial liquidity
        vm.startPrank(alice);
        yesToken.approve(address(market), 100 * PRECISION);
        noToken.approve(address(market), 100 * PRECISION);
        market.addLiquidity(100 * PRECISION, 100 * PRECISION);
        vm.stopPrank();

        // Bob trades YES for NO
        uint256 yesAmountIn = 10 * PRECISION;
        uint256 expectedNoOut = market.getNoAmountOut(yesAmountIn);

        vm.startPrank(bob);
        yesToken.approve(address(market), yesAmountIn);

        uint256 bobNoBefore = noToken.balanceOf(bob);

        vm.expectEmit(true, false, false, true);
        emit PredictionMarket.Trade(bob, false, yesAmountIn, expectedNoOut);

        market.trade(false, yesAmountIn, 0); // buyYes = false, minAmountOut = 0

        assertEq(noToken.balanceOf(bob), bobNoBefore + expectedNoOut);
        assertEq(market.yesReserves(), 110 * PRECISION);
        assertEq(market.noReserves(), 100 * PRECISION - expectedNoOut);

        vm.stopPrank();
    }

    function testTradeNoForYes() public {
        // Setup initial liquidity
        vm.startPrank(alice);
        yesToken.approve(address(market), 100 * PRECISION);
        noToken.approve(address(market), 100 * PRECISION);
        market.addLiquidity(100 * PRECISION, 100 * PRECISION);
        vm.stopPrank();

        // Bob trades NO for YES
        uint256 noAmountIn = 10 * PRECISION;
        uint256 expectedYesOut = market.getYesAmountOut(noAmountIn);

        vm.startPrank(bob);
        noToken.approve(address(market), noAmountIn);

        uint256 bobYesBefore = yesToken.balanceOf(bob);

        vm.expectEmit(true, false, false, true);
        emit PredictionMarket.Trade(bob, true, noAmountIn, expectedYesOut);

        market.trade(true, noAmountIn, 0); // buyYes = true, minAmountOut = 0

        assertEq(yesToken.balanceOf(bob), bobYesBefore + expectedYesOut);
        assertEq(market.noReserves(), 110 * PRECISION);
        assertEq(market.yesReserves(), 100 * PRECISION - expectedYesOut);

        vm.stopPrank();
    }

    function testSlippageProtection() public {
        // Setup initial liquidity
        vm.startPrank(alice);
        yesToken.approve(address(market), 100 * PRECISION);
        noToken.approve(address(market), 100 * PRECISION);
        market.addLiquidity(100 * PRECISION, 100 * PRECISION);
        vm.stopPrank();

        // Bob tries to trade with high slippage protection
        uint256 yesAmountIn = 10 * PRECISION;
        uint256 expectedNoOut = market.getNoAmountOut(yesAmountIn);
        uint256 minAmountOut = expectedNoOut + 1; // Set higher than expected

        vm.startPrank(bob);
        yesToken.approve(address(market), yesAmountIn);

        vm.expectRevert("Slippage exceeded");
        market.trade(false, yesAmountIn, minAmountOut);

        vm.stopPrank();
    }

    function testUpdateProbability() public {
        uint256 newProbability = PRECISION * 3 / 4; // 75%

        vm.startPrank(owner);

        vm.expectEmit(false, false, false, true);
        emit PredictionMarket.ProbabilityUpdated(newProbability);

        market.updateProbability(newProbability);

        assertEq(market.probability(), newProbability);

        vm.stopPrank();
    }

    function testFailUpdateProbabilityNonOwner() public {
        vm.startPrank(alice);
        market.updateProbability(PRECISION * 3 / 4);
        vm.stopPrank();
    }

    function testFailUpdateProbabilityInvalid() public {
        vm.startPrank(owner);
        market.updateProbability(PRECISION + 1); // > 100%
        vm.stopPrank();
    }

    function testResolveMarket() public {
        vm.startPrank(owner);

        vm.expectEmit(false, false, false, true);
        emit PredictionMarket.MarketResolved(true);

        market.resolveMarket(true);

        assertTrue(market.resolved());
        assertTrue(market.yesWins());

        vm.stopPrank();
    }

    function testFailResolveMarketNonOwner() public {
        vm.startPrank(alice);
        market.resolveMarket(true);
        vm.stopPrank();
    }

    function testFailResolveMarketTwice() public {
        vm.startPrank(owner);
        market.resolveMarket(true);
        market.resolveMarket(false); // Should fail
        vm.stopPrank();
    }

    function testClaimWinningsYesWins() public {
        // Setup liquidity
        vm.startPrank(alice);
        yesToken.approve(address(market), 100 * PRECISION);
        noToken.approve(address(market), 100 * PRECISION);
        market.addLiquidity(100 * PRECISION, 100 * PRECISION);
        vm.stopPrank();

        // Resolve market with YES winning
        vm.startPrank(owner);
        market.resolveMarket(true);
        vm.stopPrank();

        // Alice claims winnings
        uint256 aliceYesBefore = yesToken.balanceOf(alice);

        vm.startPrank(alice);
        market.claimWinnings();

        assertEq(market.liquidityShares(alice), 0);
        assertGt(yesToken.balanceOf(alice), aliceYesBefore);

        vm.stopPrank();
    }

    function testClaimWinningsNoWins() public {
        // Setup liquidity
        vm.startPrank(alice);
        yesToken.approve(address(market), 100 * PRECISION);
        noToken.approve(address(market), 100 * PRECISION);
        market.addLiquidity(100 * PRECISION, 100 * PRECISION);
        vm.stopPrank();

        // Resolve market with NO winning
        vm.startPrank(owner);
        market.resolveMarket(false);
        vm.stopPrank();

        // Alice claims winnings
        uint256 aliceNoBefore = noToken.balanceOf(alice);

        vm.startPrank(alice);
        market.claimWinnings();

        assertEq(market.liquidityShares(alice), 0);
        assertGt(noToken.balanceOf(alice), aliceNoBefore);

        vm.stopPrank();
    }

    function testFailClaimWinningsNotResolved() public {
        vm.startPrank(alice);
        market.claimWinnings();
        vm.stopPrank();
    }

    function testFailClaimWinningsNoShares() public {
        vm.startPrank(owner);
        market.resolveMarket(true);
        vm.stopPrank();

        vm.startPrank(alice); // Alice has no shares
        market.claimWinnings();
        vm.stopPrank();
    }

    function testCalculateLPPayoff() public {
        // Setup liquidity
        vm.startPrank(alice);
        yesToken.approve(address(market), 100 * PRECISION);
        noToken.approve(address(market), 100 * PRECISION);
        market.addLiquidity(100 * PRECISION, 100 * PRECISION);
        vm.stopPrank();

        uint256 aliceShares = market.liquidityShares(alice);

        // Test payoff calculation at different probabilities
        uint256 payoff25 = market.calculateLPPayoff(PRECISION / 4, aliceShares); // 25%
        uint256 payoff50 = market.calculateLPPayoff(PRECISION / 2, aliceShares); // 50%
        uint256 payoff75 = market.calculateLPPayoff(PRECISION * 3 / 4, aliceShares); // 75%

        assertGt(payoff25, 0);
        assertGt(payoff50, 0);
        assertGt(payoff75, 0);

        // At 50% probability with equal reserves, payoff should be close to initial investment
        assertApproxEqRel(payoff50, 100 * PRECISION, 0.01e18); // 1% tolerance
    }

    function testGetMarketState() public {
        // Setup liquidity
        vm.startPrank(alice);
        yesToken.approve(address(market), 100 * PRECISION);
        noToken.approve(address(market), 100 * PRECISION);
        market.addLiquidity(100 * PRECISION, 100 * PRECISION);
        vm.stopPrank();

        (
            uint256 yesReserves,
            uint256 noReserves,
            uint256 probability,
            uint256 k,
            uint256 totalLiquidity,
            bool resolved,
            bool yesWins
        ) = market.getMarketState();

        assertEq(yesReserves, 100 * PRECISION);
        assertEq(noReserves, 100 * PRECISION);
        assertEq(probability, INITIAL_PROBABILITY);
        assertGt(k, 0);
        assertEq(totalLiquidity, 100 * PRECISION);
        assertFalse(resolved);
        assertFalse(yesWins);
    }

    function testInvariantMaintained() public {
        // Setup liquidity
        vm.startPrank(alice);
        yesToken.approve(address(market), 100 * PRECISION);
        noToken.approve(address(market), 100 * PRECISION);
        market.addLiquidity(100 * PRECISION, 100 * PRECISION);
        vm.stopPrank();

        uint256 initialK = market.k();

        // Perform several trades
        vm.startPrank(bob);
        yesToken.approve(address(market), 50 * PRECISION);
        noToken.approve(address(market), 50 * PRECISION);

        market.trade(false, 10 * PRECISION, 0); // Sell YES for NO
        market.trade(true, 5 * PRECISION, 0);   // Sell NO for YES

        vm.stopPrank();

        // Check that invariant is approximately maintained
        uint256 finalK = market.k();
        assertApproxEqRel(finalK, initialK, 0.01e18); // 1% tolerance for rounding
    }

    function testFailTradeAfterResolution() public {
        vm.startPrank(owner);
        market.resolveMarket(true);
        vm.stopPrank();

        vm.startPrank(alice);
        yesToken.approve(address(market), 10 * PRECISION);
        market.trade(false, 10 * PRECISION, 0);
        vm.stopPrank();
    }

    function testFailAddLiquidityAfterResolution() public {
        vm.startPrank(owner);
        market.resolveMarket(true);
        vm.stopPrank();

        vm.startPrank(alice);
        yesToken.approve(address(market), 100 * PRECISION);
        noToken.approve(address(market), 100 * PRECISION);
        market.addLiquidity(100 * PRECISION, 100 * PRECISION);
        vm.stopPrank();
    }
}